@echo off
echo 🚀 ComfyUI Modal Setup (Windows)
echo ================================

echo 📦 Installing requirements...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ Failed to install requirements
    pause
    exit /b 1
)

echo 🔐 Checking Modal authentication...
modal token current >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Modal not authenticated
    echo Please run: modal token new
    pause
    exit /b 1
)

echo ✅ Modal authenticated

echo 🚀 Running Python setup...
python setup.py
if %errorlevel% neq 0 (
    echo ❌ Setup failed
    pause
    exit /b 1
)

echo 🧪 Running tests...
python test_setup.py

echo ✅ Setup complete!
echo.
echo Next steps:
echo 1. python generate.py --prompt "your prompt here"
echo 2. Check Modal dashboard: https://modal.com/apps
echo.
pause
