import modal
import os
import subprocess
import sys
from pathlib import Path

# Define the Modal app
app = modal.App("comfyui-app")

# Create a custom image with ComfyUI and dependencies
comfyui_image = (
    modal.Image.debian_slim(python_version="3.11")
    .apt_install("git", "wget", "curl", "ffmpeg", "libsm6", "libxext6")
    .pip_install(
        "torch", 
        "torchvision", 
        "torchaudio", 
        "--index-url", 
        "https://download.pytorch.org/whl/cu121"
    )
    .pip_install(
        "xformers",
        "transformers",
        "accelerate",
        "diffusers",
        "opencv-python",
        "pillow",
        "numpy",
        "requests",
        "aiohttp",
        "websockets",
        "psutil"
    )
    .run_commands(
        "cd /root && git clone https://github.com/comfyanonymous/ComfyUI.git",
        "cd /root/ComfyUI && pip install -r requirements.txt"
    )
)

# Create persistent volumes for models and outputs
models_volume = modal.Volume.from_name("comfyui-models", create_if_missing=True)
outputs_volume = modal.Volume.from_name("comfyui-outputs", create_if_missing=True)

@app.function(
    image=comfyui_image,
    gpu=modal.gpu.A10G(),  # Use A10G GPU (24GB VRAM)
    volumes={
        "/root/ComfyUI/models": models_volume,
        "/root/ComfyUI/output": outputs_volume,
    },
    timeout=3600,  # 1 hour timeout
    container_idle_timeout=300,  # 5 minutes idle timeout
)
def setup_models():
    """Download essential models for ComfyUI"""
    import requests
    from pathlib import Path
    
    models_dir = Path("/root/ComfyUI/models")
    
    # Create model directories
    (models_dir / "checkpoints").mkdir(exist_ok=True)
    (models_dir / "vae").mkdir(exist_ok=True)
    (models_dir / "clip").mkdir(exist_ok=True)
    (models_dir / "unet").mkdir(exist_ok=True)
    
    # Download SDXL base model (if not exists)
    checkpoint_path = models_dir / "checkpoints" / "sd_xl_base_1.0.safetensors"
    if not checkpoint_path.exists():
        print("Downloading SDXL base model...")
        url = "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0/resolve/main/sd_xl_base_1.0.safetensors"
        response = requests.get(url, stream=True)
        with open(checkpoint_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        print("SDXL base model downloaded!")
    
    # Download VAE
    vae_path = models_dir / "vae" / "sdxl_vae.safetensors"
    if not vae_path.exists():
        print("Downloading SDXL VAE...")
        url = "https://huggingface.co/stabilityai/sdxl-vae/resolve/main/sdxl_vae.safetensors"
        response = requests.get(url, stream=True)
        with open(vae_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        print("SDXL VAE downloaded!")
    
    return "Models setup complete!"

@app.function(
    image=comfyui_image,
    gpu=modal.gpu.A10G(),
    volumes={
        "/root/ComfyUI/models": models_volume,
        "/root/ComfyUI/output": outputs_volume,
    },
    timeout=3600,
    container_idle_timeout=300,
)
def run_comfyui_server():
    """Start ComfyUI server"""
    os.chdir("/root/ComfyUI")
    
    # Start ComfyUI server
    cmd = [
        sys.executable, "main.py",
        "--listen", "0.0.0.0",
        "--port", "8188",
        "--dont-print-server"
    ]
    
    print("Starting ComfyUI server...")
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    
    # Wait for server to start
    import time
    time.sleep(10)
    
    if process.poll() is None:
        print("ComfyUI server started successfully!")
        return "Server running on port 8188"
    else:
        stdout, stderr = process.communicate()
        print(f"Server failed to start. STDOUT: {stdout}, STDERR: {stderr}")
        return f"Server failed: {stderr}"

@app.function(
    image=comfyui_image,
    gpu=modal.gpu.A10G(),
    volumes={
        "/root/ComfyUI/models": models_volume,
        "/root/ComfyUI/output": outputs_volume,
    },
    timeout=600,
)
def generate_image(workflow_json: str, prompt: str = "a beautiful landscape"):
    """Generate image using ComfyUI workflow"""
    import json
    import requests
    import time
    import uuid
    from pathlib import Path
    
    os.chdir("/root/ComfyUI")
    
    # Start ComfyUI server in background
    server_process = subprocess.Popen([
        sys.executable, "main.py",
        "--listen", "0.0.0.0",
        "--port", "8188",
        "--dont-print-server"
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    # Wait for server to start
    time.sleep(15)
    
    try:
        # Parse workflow
        workflow = json.loads(workflow_json) if workflow_json else get_default_workflow(prompt)
        
        # Generate unique client ID
        client_id = str(uuid.uuid4())
        
        # Queue the workflow
        response = requests.post(
            "http://localhost:8188/prompt",
            json={"prompt": workflow, "client_id": client_id}
        )
        
        if response.status_code == 200:
            prompt_id = response.json()["prompt_id"]
            print(f"Queued workflow with prompt_id: {prompt_id}")
            
            # Wait for completion and get result
            result = wait_for_completion(prompt_id)
            return result
        else:
            return f"Failed to queue workflow: {response.text}"
            
    finally:
        # Clean up server process
        server_process.terminate()
        server_process.wait()

def get_default_workflow(prompt: str):
    """Get a default SDXL workflow"""
    return {
        "3": {
            "inputs": {
                "seed": 42,
                "steps": 20,
                "cfg": 8.0,
                "sampler_name": "euler",
                "scheduler": "normal",
                "denoise": 1.0,
                "model": ["4", 0],
                "positive": ["6", 0],
                "negative": ["7", 0],
                "latent_image": ["5", 0]
            },
            "class_type": "KSampler"
        },
        "4": {
            "inputs": {
                "ckpt_name": "sd_xl_base_1.0.safetensors"
            },
            "class_type": "CheckpointLoaderSimple"
        },
        "5": {
            "inputs": {
                "width": 1024,
                "height": 1024,
                "batch_size": 1
            },
            "class_type": "EmptyLatentImage"
        },
        "6": {
            "inputs": {
                "text": prompt,
                "clip": ["4", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "7": {
            "inputs": {
                "text": "text, watermark",
                "clip": ["4", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "8": {
            "inputs": {
                "samples": ["3", 0],
                "vae": ["4", 2]
            },
            "class_type": "VAEDecode"
        },
        "9": {
            "inputs": {
                "filename_prefix": "ComfyUI",
                "images": ["8", 0]
            },
            "class_type": "SaveImage"
        }
    }

def wait_for_completion(prompt_id: str, timeout: int = 300):
    """Wait for workflow completion and return result"""
    import requests
    import time
    
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"http://localhost:8188/history/{prompt_id}")
            if response.status_code == 200:
                history = response.json()
                if prompt_id in history:
                    result = history[prompt_id]
                    if result.get("status", {}).get("completed", False):
                        # Get output images
                        outputs = result.get("outputs", {})
                        images = []
                        for node_id, node_output in outputs.items():
                            if "images" in node_output:
                                for img in node_output["images"]:
                                    images.append({
                                        "filename": img["filename"],
                                        "subfolder": img.get("subfolder", ""),
                                        "type": img.get("type", "output")
                                    })
                        return {"status": "completed", "images": images}
            
            time.sleep(2)
            
        except Exception as e:
            print(f"Error checking status: {e}")
            time.sleep(2)
    
    return {"status": "timeout", "message": "Workflow timed out"}

# CLI functions
@app.local_entrypoint()
def setup():
    """Setup models and dependencies"""
    print("Setting up ComfyUI models...")
    result = setup_models.remote()
    print(result)

@app.local_entrypoint()
def generate(prompt: str = "a beautiful landscape", workflow: str = None):
    """Generate an image with ComfyUI"""
    print(f"Generating image with prompt: {prompt}")
    
    workflow_json = None
    if workflow and os.path.exists(workflow):
        with open(workflow, 'r') as f:
            workflow_json = f.read()
    
    result = generate_image.remote(workflow_json or "", prompt)
    print(f"Generation result: {result}")
    return result

if __name__ == "__main__":
    # For local testing
    print("ComfyUI Modal App")
    print("Use 'modal run comfyui_modal.py::setup' to setup models")
    print("Use 'modal run comfyui_modal.py::generate --prompt \"your prompt\"' to generate images")
