#!/usr/bin/env python3
"""
ComfyUI Image Generation Script
Generate images using ComfyUI on Modal
"""

import argparse
import json
import sys
from pathlib import Path

def load_workflow(workflow_path):
    """Load workflow from JSON file"""
    try:
        with open(workflow_path, 'r') as f:
            return f.read()
    except FileNotFoundError:
        print(f"❌ Workflow file not found: {workflow_path}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in workflow file: {e}")
        return None

def update_workflow_prompt(workflow_json, prompt):
    """Update the prompt in a workflow JSON"""
    try:
        workflow = json.loads(workflow_json)
        
        # Find text encode nodes and update prompt
        for node_id, node in workflow.items():
            if node.get("class_type") == "CLIPTextEncode":
                # Update positive prompt (usually the first text encode node)
                if "text" in node["inputs"] and not any(neg_word in node["inputs"]["text"].lower() 
                                                       for neg_word in ["negative", "bad", "worst", "low quality"]):
                    node["inputs"]["text"] = prompt
                    break
        
        return json.dumps(workflow)
    except Exception as e:
        print(f"❌ Error updating workflow prompt: {e}")
        return workflow_json

def main():
    parser = argparse.ArgumentParser(description="Generate images with ComfyUI on Modal")
    parser.add_argument("--prompt", "-p", default="a beautiful landscape", 
                       help="Text prompt for image generation")
    parser.add_argument("--workflow", "-w", 
                       help="Path to workflow JSON file")
    parser.add_argument("--seed", "-s", type=int, 
                       help="Random seed for generation")
    parser.add_argument("--steps", type=int, default=20,
                       help="Number of sampling steps")
    parser.add_argument("--cfg", type=float, default=8.0,
                       help="CFG scale")
    parser.add_argument("--width", type=int, default=1024,
                       help="Image width")
    parser.add_argument("--height", type=int, default=1024,
                       help="Image height")
    
    args = parser.parse_args()
    
    print(f"🎨 Generating image with ComfyUI")
    print(f"📝 Prompt: {args.prompt}")
    
    # Load workflow if specified
    workflow_json = None
    if args.workflow:
        workflow_json = load_workflow(args.workflow)
        if workflow_json is None:
            sys.exit(1)
        
        # Update prompt in workflow
        workflow_json = update_workflow_prompt(workflow_json, args.prompt)
        print(f"📄 Using workflow: {args.workflow}")
    
    # Import and run Modal function
    try:
        import subprocess
        
        # Build command
        cmd = ["modal", "run", "comfyui_modal.py::generate", "--prompt", args.prompt]
        
        if workflow_json:
            # Save temporary workflow file
            temp_workflow = Path("temp_workflow.json")
            with open(temp_workflow, 'w') as f:
                f.write(workflow_json)
            cmd.extend(["--workflow", str(temp_workflow)])
        
        print("🚀 Starting generation on Modal...")
        result = subprocess.run(cmd, check=True)
        
        # Clean up temp file
        if workflow_json and temp_workflow.exists():
            temp_workflow.unlink()
        
        print("✅ Generation complete!")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Generation failed: {e}")
        sys.exit(1)
    except ImportError:
        print("❌ Modal not installed. Please run: pip install modal")
        sys.exit(1)

if __name__ == "__main__":
    main()
