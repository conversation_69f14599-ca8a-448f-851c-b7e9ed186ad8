#!/usr/bin/env python3
"""
ComfyUI Modal Setup Script
This script helps you set up and deploy ComfyUI on Modal.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(cmd, description=""):
    """Run a command and handle errors"""
    print(f"🔄 {description}")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - Success")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Failed")
        print(f"Error: {e.stderr}")
        return None

def check_modal_auth():
    """Check if Modal is authenticated"""
    result = subprocess.run("modal token current", shell=True, capture_output=True, text=True)
    return result.returncode == 0

def install_requirements():
    """Install Python requirements"""
    if not run_command("pip install -r requirements.txt", "Installing requirements"):
        return False
    return True

def setup_modal():
    """Set up Modal authentication and project"""
    if not check_modal_auth():
        print("🔐 Modal authentication required")
        print("Please run: modal token new")
        print("Then visit the URL to authenticate")
        return False
    
    print("✅ Modal authentication verified")
    return True

def deploy_app():
    """Deploy the ComfyUI app to Modal"""
    if not run_command("modal deploy comfyui_modal.py", "Deploying ComfyUI app to Modal"):
        return False
    return True

def setup_models():
    """Set up ComfyUI models"""
    if not run_command("modal run comfyui_modal.py::setup", "Setting up ComfyUI models"):
        return False
    return True

def main():
    """Main setup function"""
    print("🚀 ComfyUI Modal Setup")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("comfyui_modal.py").exists():
        print("❌ comfyui_modal.py not found. Please run this script from the project root.")
        sys.exit(1)
    
    # Step 1: Install requirements
    if not install_requirements():
        print("❌ Failed to install requirements")
        sys.exit(1)
    
    # Step 2: Set up Modal
    if not setup_modal():
        print("❌ Modal setup failed")
        sys.exit(1)
    
    # Step 3: Deploy app
    if not deploy_app():
        print("❌ App deployment failed")
        sys.exit(1)
    
    # Step 4: Set up models
    print("📦 Setting up models (this may take a while)...")
    if not setup_models():
        print("❌ Model setup failed")
        sys.exit(1)
    
    print("\n🎉 Setup complete!")
    print("\nNext steps:")
    print("1. Generate an image: python generate.py --prompt 'your prompt here'")
    print("2. Use custom workflow: python generate.py --workflow workflows/basic_sdxl.json")
    print("3. Check Modal dashboard: https://modal.com/apps")

if __name__ == "__main__":
    main()
