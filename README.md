# ComfyUI on Modal

This project sets up ComfyUI to run on Modal's cloud infrastructure with powerful GPUs.

## Features

- 🚀 **Cloud-based**: Run ComfyUI on Modal's A10G GPUs (24GB VRAM)
- 📦 **Persistent Storage**: Models and outputs are stored in Modal volumes
- 🔄 **Auto-scaling**: Pay only for what you use
- 🎨 **Easy Generation**: Simple Python scripts for image generation
- 📋 **Workflow Support**: Use custom ComfyUI workflows

## Quick Start

### 1. Prerequisites

- Python 3.11+
- Modal account (sign up at [modal.com](https://modal.com))

### 2. Installation

```bash
# Clone or download this project
# Navigate to the project directory

# Install dependencies
pip install -r requirements.txt

# Authenticate with Modal
modal token new
```

### 3. Setup

Run the automated setup script:

```bash
python setup.py
```

This will:
- Deploy the ComfyUI app to Modal
- Download essential models (SDXL base + VAE)
- Set up persistent storage

### 4. Generate Images

#### Basic generation:
```bash
python generate.py --prompt "a beautiful sunset over mountains"
```

#### With custom parameters:
```bash
python generate.py \
  --prompt "a cyberpunk city at night" \
  --steps 30 \
  --cfg 9.0 \
  --width 1024 \
  --height 1024
```

#### Using custom workflows:
```bash
python generate.py \
  --prompt "your prompt here" \
  --workflow workflows/basic_sdxl.json
```

## Manual Commands

### Deploy the app:
```bash
modal deploy comfyui_modal.py
```

### Setup models:
```bash
modal run comfyui_modal.py::setup
```

### Generate with Modal directly:
```bash
modal run comfyui_modal.py::generate --prompt "your prompt"
```

## Project Structure

```
├── comfyui_modal.py      # Main Modal application
├── setup.py              # Automated setup script
├── generate.py           # Image generation script
├── requirements.txt      # Python dependencies
├── modal.toml           # Modal project configuration
├── workflows/           # ComfyUI workflow files
│   └── basic_sdxl.json  # Basic SDXL workflow
├── config/              # Configuration files
│   └── models.yaml      # Model definitions
└── README.md            # This file
```

## Configuration

### GPU Options

The default configuration uses Modal's A10G GPU (24GB VRAM). You can modify the GPU in `comfyui_modal.py`:

```python
gpu=modal.gpu.A10G()      # 24GB VRAM
gpu=modal.gpu.A100()      # 40GB VRAM  
gpu=modal.gpu.H100()      # 80GB VRAM
```

### Models

Models are automatically downloaded to persistent Modal volumes:
- **Checkpoints**: `/root/ComfyUI/models/checkpoints/`
- **VAE**: `/root/ComfyUI/models/vae/`
- **Outputs**: `/root/ComfyUI/output/`

### Custom Workflows

1. Export your workflow from ComfyUI as JSON
2. Save it in the `workflows/` directory
3. Use it with: `python generate.py --workflow workflows/your_workflow.json`

## Costs

Modal pricing (approximate):
- **A10G GPU**: ~$0.60/hour
- **Storage**: ~$0.10/GB/month
- **Bandwidth**: Free tier available

The app uses container idle timeout (5 minutes) to minimize costs.

## Troubleshooting

### Authentication Issues
```bash
modal token new
```

### Model Download Failures
```bash
modal run comfyui_modal.py::setup
```

### Generation Errors
Check Modal logs:
```bash
modal logs comfyui-app
```

## Advanced Usage

### Custom Models

Add your own models by modifying the `setup_models()` function in `comfyui_modal.py`.

### Batch Generation

Create a script to generate multiple images:

```python
import subprocess

prompts = [
    "a beautiful landscape",
    "a cyberpunk city", 
    "a fantasy castle"
]

for prompt in prompts:
    subprocess.run([
        "python", "generate.py", 
        "--prompt", prompt
    ])
```

### API Integration

The Modal functions can be called from other Python scripts:

```python
from comfyui_modal import generate_image

result = generate_image.remote("", "your prompt here")
print(result)
```

## Support

- Modal Documentation: [docs.modal.com](https://docs.modal.com)
- ComfyUI Documentation: [github.com/comfyanonymous/ComfyUI](https://github.com/comfyanonymous/ComfyUI)

## License

This project is open source. ComfyUI and Modal have their own respective licenses.
