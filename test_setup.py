#!/usr/bin/env python3
"""
Test script for ComfyUI Modal setup
Validates that everything is working correctly
"""

import subprocess
import sys
import json
from pathlib import Path

def run_command(cmd, description="", capture_output=True):
    """Run a command and return result"""
    print(f"🔄 {description}")
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            check=True, 
            capture_output=capture_output, 
            text=True
        )
        print(f"✅ {description} - Success")
        return result.stdout if capture_output else True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Failed")
        if capture_output:
            print(f"Error: {e.stderr}")
        return None

def test_modal_auth():
    """Test Modal authentication"""
    result = subprocess.run("modal token current", shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        print("✅ Modal authentication - OK")
        return True
    else:
        print("❌ Modal authentication - Failed")
        print("Please run: modal token new")
        return False

def test_modal_app_exists():
    """Test if Modal app is deployed"""
    result = run_command("modal app list", "Checking Modal apps")
    if result and "comfyui-app" in result:
        print("✅ ComfyUI app deployed - OK")
        return True
    else:
        print("❌ ComfyUI app not found")
        return False

def test_workflow_files():
    """Test workflow files exist and are valid JSON"""
    workflow_dir = Path("workflows")
    if not workflow_dir.exists():
        print("❌ Workflows directory not found")
        return False
    
    json_files = list(workflow_dir.glob("*.json"))
    if not json_files:
        print("❌ No workflow files found")
        return False
    
    for json_file in json_files:
        try:
            with open(json_file, 'r') as f:
                json.load(f)
            print(f"✅ Workflow {json_file.name} - Valid JSON")
        except json.JSONDecodeError as e:
            print(f"❌ Workflow {json_file.name} - Invalid JSON: {e}")
            return False
    
    return True

def test_requirements():
    """Test that required packages are installed"""
    required_packages = ["modal", "requests", "pillow", "numpy"]
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ Package {package} - Installed")
        except ImportError:
            print(f"❌ Package {package} - Not installed")
            return False
    
    return True

def test_project_structure():
    """Test project structure"""
    required_files = [
        "comfyui_modal.py",
        "setup.py", 
        "generate.py",
        "requirements.txt",
        "modal.toml",
        "README.md"
    ]
    
    required_dirs = [
        "workflows",
        "config"
    ]
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ File {file_path} - Exists")
        else:
            print(f"❌ File {file_path} - Missing")
            return False
    
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✅ Directory {dir_path} - Exists")
        else:
            print(f"❌ Directory {dir_path} - Missing")
            return False
    
    return True

def test_modal_volumes():
    """Test Modal volumes exist"""
    result = run_command("modal volume list", "Checking Modal volumes")
    if result:
        if "comfyui-models" in result and "comfyui-outputs" in result:
            print("✅ Modal volumes - OK")
            return True
        else:
            print("⚠️  Modal volumes not found (will be created on first run)")
            return True
    return False

def run_basic_test():
    """Run a basic generation test"""
    print("\n🧪 Running basic generation test...")
    print("This will take a few minutes and may incur Modal costs.")
    
    response = input("Continue with test generation? (y/N): ")
    if response.lower() != 'y':
        print("⏭️  Skipping generation test")
        return True
    
    # Run a simple generation
    cmd = 'modal run comfyui_modal.py::generate --prompt "test image, simple"'
    result = run_command(cmd, "Testing image generation", capture_output=False)
    
    return result is not None

def main():
    """Main test function"""
    print("🧪 ComfyUI Modal Setup Test")
    print("=" * 50)
    
    tests = [
        ("Project Structure", test_project_structure),
        ("Required Packages", test_requirements),
        ("Workflow Files", test_workflow_files),
        ("Modal Authentication", test_modal_auth),
        ("Modal App Deployment", test_modal_app_exists),
        ("Modal Volumes", test_modal_volumes),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Testing: {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} failed")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        
        # Offer to run generation test
        run_basic_test()
        
        print("\n✅ Setup validation complete!")
        print("\nYour ComfyUI Modal setup is ready to use!")
        print("\nNext steps:")
        print("1. python generate.py --prompt 'your prompt here'")
        print("2. Check Modal dashboard: https://modal.com/apps")
        
    else:
        print(f"\n❌ {total - passed} tests failed")
        print("Please fix the issues above before using the setup.")
        sys.exit(1)

if __name__ == "__main__":
    main()
