# ComfyUI Models Configuration
models:
  checkpoints:
    - name: "sd_xl_base_1.0.safetensors"
      url: "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0/resolve/main/sd_xl_base_1.0.safetensors"
      size: "6.94 GB"
      description: "Stable Diffusion XL Base Model"
    
    - name: "sd_xl_refiner_1.0.safetensors"
      url: "https://huggingface.co/stabilityai/stable-diffusion-xl-refiner-1.0/resolve/main/sd_xl_refiner_1.0.safetensors"
      size: "6.08 GB"
      description: "Stable Diffusion XL Refiner Model"
      optional: true

  vae:
    - name: "sdxl_vae.safetensors"
      url: "https://huggingface.co/stabilityai/sdxl-vae/resolve/main/sdxl_vae.safetensors"
      size: "334 MB"
      description: "SDXL VAE"

  controlnet:
    - name: "control_v11p_sd15_canny.pth"
      url: "https://huggingface.co/lllyasviel/ControlNet-v1-1/resolve/main/control_v11p_sd15_canny.pth"
      size: "1.44 GB"
      description: "ControlNet Canny"
      optional: true

  upscale_models:
    - name: "RealESRGAN_x4plus.pth"
      url: "https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth"
      size: "64 MB"
      description: "Real-ESRGAN 4x Upscaler"
      optional: true

# GPU Requirements
gpu_requirements:
  minimum_vram: "8GB"
  recommended_vram: "24GB"
  supported_gpus:
    - "A10G (24GB)"
    - "A100 (40GB/80GB)"
    - "H100 (80GB)"
