"""
ComfyUI Modal Configuration Settings
"""

# Modal Configuration
MODAL_APP_NAME = "comfyui-app"
MODAL_GPU = "A10G"  # Options: A10G, A100, H100
MODAL_TIMEOUT = 3600  # 1 hour
MODAL_IDLE_TIMEOUT = 300  # 5 minutes

# ComfyUI Configuration
COMFYUI_PORT = 8188
COMFYUI_HOST = "0.0.0.0"

# Default Generation Settings
DEFAULT_SETTINGS = {
    "width": 1024,
    "height": 1024,
    "steps": 20,
    "cfg": 8.0,
    "sampler_name": "euler",
    "scheduler": "normal",
    "seed": 42,
    "batch_size": 1
}

# Model URLs
MODEL_URLS = {
    "sdxl_base": "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0/resolve/main/sd_xl_base_1.0.safetensors",
    "sdxl_refiner": "https://huggingface.co/stabilityai/stable-diffusion-xl-refiner-1.0/resolve/main/sd_xl_refiner_1.0.safetensors",
    "sdxl_vae": "https://huggingface.co/stabilityai/sdxl-vae/resolve/main/sdxl_vae.safetensors",
    "controlnet_canny": "https://huggingface.co/lllyasviel/ControlNet-v1-1/resolve/main/control_v11p_sd15_canny.pth",
    "upscaler": "https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth"
}

# Volume Names
VOLUMES = {
    "models": "comfyui-models",
    "outputs": "comfyui-outputs"
}

# Paths
PATHS = {
    "comfyui_root": "/root/ComfyUI",
    "models": "/root/ComfyUI/models",
    "outputs": "/root/ComfyUI/output",
    "checkpoints": "/root/ComfyUI/models/checkpoints",
    "vae": "/root/ComfyUI/models/vae",
    "controlnet": "/root/ComfyUI/models/controlnet",
    "upscale_models": "/root/ComfyUI/models/upscale_models"
}
